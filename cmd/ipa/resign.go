package ipa

import (
	"archive/zip"
	"bytes"
	"fmt"
	"image"
	_ "image/png"
	"io"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"howett.net/plist"
)

// ResignConfig 重签名配置
type ResignConfig struct {
	IPAPath             string
	OutputPath          string
	BundleID            string
	ProvisioningProfile string
	Certificate         string
	DisplayName         string
	AppVersion          string
	BuildVersion        string
	AppIconPath         string
	LaunchImagePath     string
	TempDir             string
	OriginAppUnzipPath  string // 原IPA解压后的目录路径，位于${TempDir}/unzipOriginApp
	AppName             string
	ForceSwiftUpdate    bool
	EntitlementsPath    string // 自定义entitlements.plist文件路径
	KeepTempDir         bool   // 是否保留临时文件目录，用于调试，默认为false（处理完成后删除临时文件）
	AdditionalPlistPath string // 附加的plist文件路径，将合并到app/Info.plist中
	RemovePlistKeys     string // 要从Info.plist中删除的最外层key，多个key用逗号分隔
}

// InfoPlist Info.plist结构
type InfoPlist struct {
	CFBundleIdentifier         string `plist:"CFBundleIdentifier"`
	CFBundleDisplayName        string `plist:"CFBundleDisplayName"`
	CFBundleShortVersionString string `plist:"CFBundleShortVersionString"`
	CFBundleVersion            string `plist:"CFBundleVersion"`
	CFBundleName               string `plist:"CFBundleName"`
}

// 清理临时目录
func cleanupResign(config *ResignConfig) {
	if config.TempDir != "" && !config.KeepTempDir {
		logInfo("清理临时目录: %s", config.TempDir)
		os.RemoveAll(config.TempDir)
	} else if config.KeepTempDir {
		logInfo("保留临时目录用于调试: %s", config.TempDir)
		fmt.Printf("临时目录已保留，路径: %s\n", config.TempDir)
	}
}

// 更新Info.plist文件
func updateInfoPlist(plistPath string, config *ResignConfig) error {
	logInfo("更新Info.plist文件: %s", plistPath)

	data, err := os.ReadFile(plistPath)
	if err != nil {
		return fmt.Errorf("读取Info.plist失败: %w", err)
	}

	var plistData map[string]interface{}
	_, err = plist.Unmarshal(data, &plistData)
	if err != nil {
		return fmt.Errorf("解析Info.plist失败: %w", err)
	}

	// 更新Bundle ID
	if config.BundleID != "" {
		plistData["CFBundleIdentifier"] = config.BundleID
		logInfo("更新Bundle ID: %s", config.BundleID)
	}

	// 更新显示名称
	if config.DisplayName != "" {
		plistData["CFBundleDisplayName"] = config.DisplayName
		plistData["CFBundleName"] = config.DisplayName
		logInfo("更新显示名称: %s", config.DisplayName)
	}

	// 更新版本号
	if config.AppVersion != "" {
		plistData["CFBundleShortVersionString"] = config.AppVersion
		logInfo("更新应用版本: %s", config.AppVersion)
	}

	// 更新构建版本
	if config.BuildVersion != "" {
		plistData["CFBundleVersion"] = config.BuildVersion
		logInfo("更新构建版本: %s", config.BuildVersion)
	}

	// 写回文件
	output, err := plist.MarshalIndent(plistData, plist.XMLFormat, "\t")
	if err != nil {
		return fmt.Errorf("序列化Info.plist失败: %w", err)
	}

	err = os.WriteFile(plistPath, output, 0644)
	if err != nil {
		return fmt.Errorf("写入Info.plist失败: %w", err)
	}

	return nil
}

// processPlistModifications 处理Info.plist文件的修改和合并
// 包括删除指定的key和合并附加的plist文件
func processPlistModifications(plistPath string, additionalPlistPath string, removePlistKeys string) error {
	// 读取原始的Info.plist文件
	data, err := os.ReadFile(plistPath)
	if err != nil {
		return fmt.Errorf("读取Info.plist失败: %w", err)
	}

	var plistData map[string]interface{}
	_, err = plist.Unmarshal(data, &plistData)
	if err != nil {
		return fmt.Errorf("解析Info.plist失败: %w", err)
	}

	// 处理要删除的key
	if removePlistKeys != "" {
		keysToRemove := strings.Split(removePlistKeys, ",")
		for _, key := range keysToRemove {
			key = strings.TrimSpace(key)
			if key == "" {
				continue
			}

			if _, exists := plistData[key]; exists {
				delete(plistData, key)
				logInfo("已删除Info.plist中的key: %s", key)
			} else {
				logWarning("Info.plist中不存在key: %s", key)
			}
		}
	}

	// 处理附加的plist文件
	if additionalPlistPath != "" && utility.IsExist(additionalPlistPath) {
		logInfo("合并附加的plist文件: %s", additionalPlistPath)

		// 解析附加的plist文件
		additionalData, err := os.ReadFile(additionalPlistPath)
		if err != nil {
			return fmt.Errorf("读取附加的plist文件失败: %w", err)
		}

		var additionalPlistData map[string]interface{}
		_, err = plist.Unmarshal(additionalData, &additionalPlistData)
		if err != nil {
			return fmt.Errorf("解析附加的plist文件失败: %w", err)
		}

		// 合并最外层key
		for key, value := range additionalPlistData {
			plistData[key] = value
			logInfo("合并key: %s", key)
		}

		logSuccess("成功合并附加的plist文件到Info.plist")
	}

	// 将修改后的内容写回Info.plist
	output, err := plist.MarshalIndent(plistData, plist.XMLFormat, "\t")
	if err != nil {
		return fmt.Errorf("序列化修改后的Info.plist失败: %w", err)
	}

	err = os.WriteFile(plistPath, output, 0644)
	if err != nil {
		return fmt.Errorf("写入修改后的Info.plist失败: %w", err)
	}

	return nil
}

// 替换应用图标
func replaceAppIcon(appPath string, iconPath string, tempDir string) error {
	if iconPath == "" {
		return nil
	}

	logInfo("替换应用图标: %s", iconPath)

	// 将iconPath按逗号分隔为数组
	iconPathsArray := strings.Split(iconPath, ",")
	var validIconPaths []string

	// 遍历每个图标路径，验证其有效性
	for _, path := range iconPathsArray {
		// 去除可能的空格
		path = strings.TrimSpace(path)
		if path == "" {
			continue
		}

		// 1. 检查iconPath是否存在
		if _, err := os.Stat(path); os.IsNotExist(err) {
			return fmt.Errorf("图标文件不存在: %s", path)
		}

		// 2. 验证图标格式和尺寸
		file, err := os.Open(path)
		if err != nil {
			return fmt.Errorf("无法打开图标文件: %s, 错误: %v", path, err)
		}

		// 检查是否为PNG格式
		img, format, err := image.DecodeConfig(file)
		if err != nil {
			file.Close()
			return fmt.Errorf("无法解析图标文件: %s, 错误: %v", path, err)
		}

		if format != "png" {
			file.Close()
			return fmt.Errorf("图标必须是PNG格式，当前格式: %s, 文件: %s", format, path)
		}

		// 检查尺寸是否为1024x1024
		if img.Width != 1024 || img.Height != 1024 {
			file.Close()
			return fmt.Errorf("图标尺寸必须是1024x1024像素，当前尺寸: %dx%d, 文件: %s", img.Width, img.Height, path)
		}

		// 重置文件指针
		file.Seek(0, 0)

		// 检查是否包含alpha通道（这需要完整解码图像）
		fullImg, _, err := image.Decode(file)
		file.Close()
		if err != nil {
			return fmt.Errorf("无法完全解码图标: %s, 错误: %v", path, err)
		}

		// 检查是否有alpha通道
		if _, hasAlpha := fullImg.(interface{ Opaque() bool }); hasAlpha {
			// 如果图像实现了Opaque()方法，检查它是否完全不透明
			opaqueImg := fullImg.(interface{ Opaque() bool })
			if !opaqueImg.Opaque() {
				return fmt.Errorf("图标不能包含alpha通道（透明度）: %s", path)
			}
		}

		// 通过所有验证，添加到有效图标路径列表
		validIconPaths = append(validIconPaths, path)
		logSuccess("图标验证通过: %s", path)
	}

	// 所有图标都已通过验证
	logInfo("共有 %d 个有效图标文件", len(validIconPaths))

	// 3. 调用alt_icons生成图标
	logInfo("生成应用图标资源...")
	// 创建输出目录在config.TempDir下
	outputDir := filepath.Join(tempDir, "newAppIcon")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建图标输出目录失败: %v", err)
	}

	// 使用GenerateAlternateIconsExe函数直接生成图标，传入所有有效的图标路径
	err := GenerateAlternateIconsExe(validIconPaths, "", outputDir)
	if err != nil {
		return fmt.Errorf("生成图标资源失败: %v", err)
	}

	// 4. 扫描并删除现有的AppIcon文件
	logInfo("扫描并删除现有的AppIcon文件...")
	files, err := os.ReadDir(appPath)
	if err != nil {
		return fmt.Errorf("读取应用目录失败: %v", err)
	}

	// 删除以AppIcon开头，以.png结尾的文件
	var deletedCount int
	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "AppIcon") && strings.HasSuffix(file.Name(), ".png") {
			filePath := filepath.Join(appPath, file.Name())
			if err := os.Remove(filePath); err != nil {
				logWarning("删除原有图标文件失败 %s: %v", file.Name(), err)
			} else {
				deletedCount++
				logInfo("已删除原有图标文件: %s", file.Name())
			}
		}
	}
	logInfo("共删除 %d 个原有图标文件", deletedCount)

	// 5. 替换生成的图标文件到应用目录
	buildDir := filepath.Join(tempDir, "newAppIcon", "build")
	if _, err := os.Stat(buildDir); os.IsNotExist(err) {
		return fmt.Errorf("未找到生成的图标资源目录: %s", buildDir)
	}

	// 查找并替换.png文件
	pngFiles, err := filepath.Glob(filepath.Join(buildDir, "*.png"))
	if err != nil {
		return fmt.Errorf("查找生成的PNG文件失败: %v", err)
	}

	for _, pngFile := range pngFiles {
		fileName := filepath.Base(pngFile)
		targetPath := filepath.Join(appPath, fileName)

		// 强制替换，即使目标文件不存在
		if err := copyFile(pngFile, targetPath); err != nil {
			logWarning("替换图标文件失败 %s: %v", fileName, err)
		} else {
			logSuccess("成功替换图标: %s", fileName)
		}
	}

	// 查找并替换Assets.car文件
	carFile := filepath.Join(buildDir, "Assets.car")
	if _, err := os.Stat(carFile); err == nil {
		targetCarPath := filepath.Join(appPath, "Assets.car")
		if err := copyFile(carFile, targetCarPath); err != nil {
			logWarning("替换Assets.car文件失败: %v", err)
		} else {
			logSuccess("成功替换Assets.car文件")
		}
	}

	// 6. 合并partial.plist和目标.app下的Info.plist
	partialPlistPath := filepath.Join(buildDir, "partial.plist")
	if _, err := os.Stat(partialPlistPath); err == nil {
		logInfo("检测到partial.plist文件，准备合并到Info.plist")

		targetInfoPlistPath := filepath.Join(appPath, "Info.plist")
		if _, err := os.Stat(targetInfoPlistPath); err == nil {
			// 调用MergePlistFiles方法合并plist文件
			mergedMap, err := utility.MergePlistFiles(targetInfoPlistPath, partialPlistPath)
			if err != nil {
				logWarning("合并partial.plist到Info.plist失败: %v", err)
			} else {
				// 将合并后的内容写回Info.plist
				output, err := plist.MarshalIndent(mergedMap, plist.XMLFormat, "\t")
				if err != nil {
					logWarning("序列化合并后的Info.plist失败: %v", err)
				} else {
					err = os.WriteFile(targetInfoPlistPath, output, 0644)
					if err != nil {
						logWarning("写入合并后的Info.plist失败: %v", err)
					} else {
						logSuccess("成功合并partial.plist到Info.plist")
					}
				}
			}
		} else {
			logWarning("未找到目标Info.plist文件: %s", targetInfoPlistPath)
		}
	} else {
		logInfo("未检测到partial.plist文件，跳过合并步骤")
	}

	logSuccess("应用图标替换完成")
	return nil
}

// 替换启动图片
func replaceLaunchImage(appPath string, launchImagePath string) error {
	if launchImagePath == "" {
		return nil
	}

	logInfo("替换启动图片: %s", launchImagePath)

	// 查找现有的启动图片文件
	launchImageFiles := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"Default.png",
		"<EMAIL>",
		"<EMAIL>",
	}

	for _, launchFile := range launchImageFiles {
		targetPath := filepath.Join(appPath, launchFile)
		if _, err := os.Stat(targetPath); err == nil {
			// 复制新启动图片
			if err := copyFile(launchImagePath, targetPath); err != nil {
				logWarning("替换启动图片失败 %s: %v", launchFile, err)
			} else {
				logSuccess("成功替换启动图片: %s", launchFile)
			}
		}
	}

	return nil
}

// 安装mobileprovision文件
func installProvisioningProfile(appPath string, provisionPath string) error {
	if provisionPath == "" {
		return nil
	}

	logInfo("安装mobileprovision文件: %s", provisionPath)

	targetPath := filepath.Join(appPath, "embedded.mobileprovision")
	err := copyFile(provisionPath, targetPath)
	if err != nil {
		return fmt.Errorf("安装mobileprovision失败: %w", err)
	}

	logSuccess("成功安装mobileprovision文件")
	return nil
}

// 签名单个文件
func signFile(filePath string, certName string, entitlements string) error {
	args := []string{"-f", "-s", certName}

	// 如果有entitlements文件，添加到参数中
	if entitlements != "" {
		args = append(args, "--entitlements", entitlements)
	}

	args = append(args, filePath)

	cmd := exec.Command("codesign", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("签名文件失败 %s: %s, %w", filePath, string(output), err)
	}
	return nil
}

// 从应用二进制文件中提取entitlements
func extractEntitlementsFromBinary(binaryPath, outputPath string) error {
	logInfo("从应用二进制文件提取entitlements: %s", binaryPath)

	// 使用shell命令直接将entitlements写入文件
	// 这样可以避免Go处理输出时可能出现的问题
	shellCmd := fmt.Sprintf("codesign --display --entitlements :- \"%s\" > \"%s\" 2>/dev/null", binaryPath, outputPath)
	cmd := exec.Command("bash", "-c", shellCmd)
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("提取entitlements失败: %w", err)
	}

	// 检查文件是否存在且不为空
	fileInfo, err := os.Stat(outputPath)
	if err != nil || fileInfo.Size() == 0 {
		return fmt.Errorf("提取的entitlements为空或文件不存在")
	}

	// 读取文件内容
	data, err := os.ReadFile(outputPath)
	if err != nil {
		return fmt.Errorf("读取entitlements文件失败: %w", err)
	}

	// 检查是否包含XML头
	if !bytes.Contains(data, []byte("<?xml")) {
		return fmt.Errorf("提取的entitlements不是有效的XML格式")
	}

	// 尝试解析XML
	var entitlements map[string]interface{}
	_, err = plist.Unmarshal(data, &entitlements)
	if err != nil {
		logWarning("解析entitlements失败，尝试手动提取关键信息: %v", err)

		// 手动检查是否包含associated-domains
		content := string(data)
		if strings.Contains(content, "com.apple.developer.associated-domains") {
			logInfo("检测到associated-domains权限，将在合并时特殊处理")
		}
	} else {
		// 检查是否包含associated-domains
		if domains, ok := entitlements["com.apple.developer.associated-domains"]; ok {
			logInfo("检测到associated-domains权限: %v", domains)
		}
	}

	logSuccess("成功提取entitlements到文件: %s", outputPath)
	return nil
}

// 从mobileprovision文件中提取entitlements并与原应用的entitlements合并
// 当未指定自定义entitlements.plist文件时，使用此函数处理
func extractEntitlements(provisionPath, originAppUnzipPath string) (string, error) {
	if provisionPath == "" {
		return "", nil
	}

	appPath := filepath.Join(originAppUnzipPath, "Payload")
	// 查找.app目录
	appDirs, err := os.ReadDir(appPath)
	if err != nil {
		return "", fmt.Errorf("读取Payload目录失败: %w", err)
	}

	var appDir string
	for _, dir := range appDirs {
		if dir.IsDir() && strings.HasSuffix(dir.Name(), ".app") {
			appDir = dir.Name()
			break
		}
	}

	if appDir == "" {
		return "", fmt.Errorf("未找到.app目录")
	}

	// 最终输出的entitlements文件路径
	tempDir := filepath.Dir(originAppUnzipPath) // 获取父目录作为临时目录
	entitlementsPath := filepath.Join(tempDir, "entitlements.plist")

	// 从原始应用中提取entitlements
	oldEntitlementsPath := filepath.Join(tempDir, "entitlements_old.plist")
	appBinaryPath := filepath.Join(appPath, appDir)

	// 查找应用二进制文件
	var appBinary string
	infoPlistPath := filepath.Join(appBinaryPath, "Info.plist")
	if _, err := os.Stat(infoPlistPath); err == nil {
		// 读取Info.plist获取可执行文件名
		data, err := os.ReadFile(infoPlistPath)
		if err == nil {
			var plistData map[string]interface{}
			if _, err := plist.Unmarshal(data, &plistData); err == nil {
				if execName, ok := plistData["CFBundleExecutable"].(string); ok {
					appBinary = filepath.Join(appBinaryPath, execName)
				}
			}
		}
	}

	// 如果无法从Info.plist获取，则使用目录名（去掉.app后缀）
	if appBinary == "" {
		execName := strings.TrimSuffix(appDir, ".app")
		appBinary = filepath.Join(appBinaryPath, execName)
	}

	// 使用codesign命令从原应用提取entitlements
	logInfo("从原应用提取entitlements: %s", appBinary)
	shellCmd := fmt.Sprintf("codesign --display --entitlements :- \"%s\" > \"%s\" 2>/dev/null", appBinary, oldEntitlementsPath)
	cmd := exec.Command("bash", "-c", shellCmd)
	err = cmd.Run()

	var oldEntitlements map[string]interface{}
	if err == nil {
		// 读取提取的entitlements
		oldData, err := os.ReadFile(oldEntitlementsPath)
		if err == nil {
			_, err = plist.Unmarshal(oldData, &oldEntitlements)
			if err != nil {
				logWarning("解析原应用entitlements失败: %v", err)

				// 尝试手动解析XML，特别是提取associated-domains
				xmlStr := string(oldData)

				// 创建一个空的entitlements映射
				oldEntitlements = make(map[string]interface{})

				// 查找com.apple.developer.associated-domains部分
				if strings.Contains(xmlStr, "com.apple.developer.associated-domains") {
					logInfo("找到com.apple.developer.associated-domains节点，尝试手动提取")

					// 提取associated-domains的值
					// 使用正则表达式匹配<key>com.apple.developer.associated-domains</key>后面的<array>...</array>部分
					domainRegex := regexp.MustCompile(`(?s)<key>com\.apple\.developer\.associated-domains</key>\s*<array>(.*?)</array>`)
					matches := domainRegex.FindStringSubmatch(xmlStr)

					if len(matches) > 1 {
						// 提取字符串值
						stringRegex := regexp.MustCompile(`<string>(.*?)</string>`)
						stringMatches := stringRegex.FindAllStringSubmatch(matches[1], -1)

						domains := make([]interface{}, 0)
						for _, match := range stringMatches {
							if len(match) > 1 {
								domains = append(domains, match[1])
								logInfo("手动提取到associated-domain: %s", match[1])
							}
						}

						if len(domains) > 0 {
							oldEntitlements["com.apple.developer.associated-domains"] = domains
							logInfo("成功手动提取associated-domains，共 %d 个域名", len(domains))
						}
					}
				}
			} else {
				logSuccess("成功从原应用提取entitlements")

				// 调试输出，查看提取的entitlements内容
				for key, value := range oldEntitlements {
					logInfo("原应用entitlement: %s = %v", key, value)
				}
			}
		} else {
			logWarning("读取原应用entitlements文件失败: %v", err)
			oldEntitlements = make(map[string]interface{})
		}
	} else {
		logWarning("从原应用提取entitlements失败: %v", err)
		oldEntitlements = make(map[string]interface{})
	}

	// 从新的mobileprovision提取entitlements
	newEntitlementsPath := filepath.Join(tempDir, "entitlements_new.plist")

	// 使用security命令提取entitlements
	logInfo("从mobileprovision提取entitlements: %s", provisionPath)
	cmdNew := exec.Command("security", "cms", "-D", "-i", provisionPath)
	output, err := cmdNew.Output()
	if err != nil {
		return "", fmt.Errorf("提取mobileprovision失败: %w", err)
	}

	// 解析plist内容，提取Entitlements
	var provisionData map[string]interface{}
	_, err = plist.Unmarshal(output, &provisionData)
	if err != nil {
		return "", fmt.Errorf("解析mobileprovision失败: %w", err)
	}

	newEntitlements, ok := provisionData["Entitlements"].(map[string]interface{})
	if !ok {
		logWarning("mobileprovision中未找到Entitlements")
		newEntitlements = make(map[string]interface{})
	} else {
		logSuccess("成功从mobileprovision提取entitlements")
	}

	// 将新的entitlements写入文件（用于调试）
	newEntitlementsBytes, _ := plist.MarshalIndent(newEntitlements, plist.XMLFormat, "\t")
	_ = os.WriteFile(newEntitlementsPath, newEntitlementsBytes, 0644)

	// 合并entitlements
	logInfo("合并entitlements...")
	mergedEntitlements := make(map[string]interface{})

	// 如果原应用有entitlements，以它为基础
	if len(oldEntitlements) > 0 {
		// 以原有entitlements中的key为主
		for key, oldValue := range oldEntitlements {
			// 特殊处理com.apple.developer.associated-domains
			if key == "com.apple.developer.associated-domains" {
				// 保留原应用的associated-domains权限
				mergedEntitlements[key] = oldValue
				logInfo("保留原应用的associated-domains权限: %v", oldValue)
			} else if newValue, exists := newEntitlements[key]; exists {
				// 如果新的mobileprovision中也有这个key，使用新值
				mergedEntitlements[key] = newValue
				logInfo("使用新mobileprovision的权限: %s = %v", key, newValue)
			} else {
				// 如果新的mobileprovision中没有这个key，则不添加
				logInfo("移除权限（新mobileprovision中不存在）: %s", key)
			}
		}
	} else {
		// 如果原应用没有entitlements，直接使用新的
		mergedEntitlements = newEntitlements
		logInfo("使用新mobileprovision的所有权限")
	}

	// 将合并后的entitlements写入文件
	mergedEntitlementsBytes, err := plist.MarshalIndent(mergedEntitlements, plist.XMLFormat, "\t")
	if err != nil {
		return "", fmt.Errorf("序列化合并后的entitlements失败: %w", err)
	}

	err = os.WriteFile(entitlementsPath, mergedEntitlementsBytes, 0644)
	if err != nil {
		return "", fmt.Errorf("写入合并后的entitlements文件失败: %w", err)
	}

	logSuccess("成功生成合并后的entitlements文件: %s", entitlementsPath)
	return entitlementsPath, nil
}

// 签名应用和动态库
func signApp(config *ResignConfig) error {
	appPath := filepath.Join(config.OriginAppUnzipPath, "Payload", config.AppName+".app")

	// 获取证书名称
	certName := ""
	if config.Certificate != "" {
		// 直接使用传入的证书名称
		certName = config.Certificate
		logInfo("使用证书: %s", certName)
	} else {
		// 如果没有指定证书，尝试使用默认的开发者证书
		certName = "iPhone Developer"
		logInfo("使用默认证书: %s", certName)
	}

	// 获取entitlements
	var entitlementsPath string
	if config.EntitlementsPath != "" {
		// 使用外部传入的entitlements文件
		entitlementsPath = config.EntitlementsPath
		logInfo("使用外部传入的entitlements文件: %s", entitlementsPath)
	} else {
		// 从原应用和mobileprovision中提取并合并entitlements
		extractedPath, err := extractEntitlements(config.ProvisioningProfile, config.OriginAppUnzipPath)
		if err != nil {
			logWarning("提取和合并entitlements失败: %v", err)
		} else {
			entitlementsPath = extractedPath
			logInfo("成功提取和合并entitlements: %s", entitlementsPath)
		}
	}

	// 首先签名Frameworks目录中的动态库和Framework
	frameworksPath := filepath.Join(appPath, "Frameworks")
	if _, err := os.Stat(frameworksPath); err == nil {
		logInfo("签名动态库和Framework...")

		// 收集所有需要签名的文件
		var dylibFiles []string
		var frameworkDirs []string

		err := filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 收集.dylib文件
			if !info.IsDir() && strings.HasSuffix(info.Name(), ".dylib") {
				dylibFiles = append(dylibFiles, path)
			}

			// 收集.framework目录
			if info.IsDir() && strings.HasSuffix(info.Name(), ".framework") {
				frameworkDirs = append(frameworkDirs, path)
			}

			return nil
		})
		if err != nil {
			return fmt.Errorf("遍历Frameworks目录失败: %w", err)
		}

		// 先签名独立的.dylib文件
		for _, dylibPath := range dylibFiles {
			logInfo("签名动态库: %s", filepath.Base(dylibPath))
			if err := signFile(dylibPath, certName, ""); err != nil {
				logWarning("签名动态库失败 %s: %v", filepath.Base(dylibPath), err)
			} else {
				logSuccess("成功签名动态库: %s", filepath.Base(dylibPath))
			}
		}

		// 然后签名Framework
		for _, frameworkPath := range frameworkDirs {
			frameworkName := filepath.Base(frameworkPath)
			logInfo("签名Framework: %s", frameworkName)
			// 签名整个Framework
			if err := signFile(frameworkPath, certName, ""); err != nil {
				logWarning("签名Framework失败 %s: %v", frameworkName, err)
			} else {
				logSuccess("成功签名Framework: %s", frameworkName)
			}
		}
	}

	// 最后签名主应用
	logInfo("签名主应用: %s", config.AppName)
	if err := signFile(appPath, certName, entitlementsPath); err != nil {
		return fmt.Errorf("签名主应用失败: %w", err)
	}

	logSuccess("成功签名主应用")

	// 验证签名（对.app进行验证而不是.ipa）
	logInfo("验证应用签名...")
	cmd := exec.Command("codesign", "-dvvv", appPath)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("签名验证失败: %s, %w", string(output), err)
	} else {
		logInfo("签名验证通过,应用签名信息: %s", string(output))
	}

	// 显示签名信息
	cmd = exec.Command("codesign", "-d", "--entitlements", "-", appPath)
	if output, err := cmd.CombinedOutput(); err == nil {
		logInfo("entitlements签名信息: %s", string(output))
	}

	return nil
}

// 检查是否需要处理SwiftSupport
func needHandleSwiftSupport(config *ResignConfig) bool {
	// 检查是否存在SwiftSupport目录
	swiftSupportPath := filepath.Join(config.OriginAppUnzipPath, "SwiftSupport")
	hasSwiftSupport := false
	if _, err := os.Stat(swiftSupportPath); err == nil {
		hasSwiftSupport = true
		logInfo("检测到原IPA包中包含SwiftSupport目录")
	}

	// 如果有强制更新参数或者存在SwiftSupport目录，则需要处理
	if config.ForceSwiftUpdate || hasSwiftSupport {
		return true
	}

	logInfo("跳过SwiftSupport处理（未设置强制更新且未检测到SwiftSupport目录）")
	return false
}

// 处理SwiftSupport（如果需要）
func handleSwiftSupport(config *ResignConfig) error {
	if !needHandleSwiftSupport(config) {
		return nil
	}

	logInfo("处理SwiftSupport...")

	// 创建临时的SwiftSupport配置
	swiftConfig := &Config{
		IPAPath:      filepath.Join(config.TempDir, "temp.ipa"),
		TempDir:      config.OriginAppUnzipPath, // 使用原IPA解压目录作为工作目录
		AppName:      config.AppName,
		ForceUpdate:  true,
		ToolchainDir: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib",
		NeedUpdate:   true,
	}

	return processSwiftSupport(swiftConfig)
}

// 创建重签名后的IPA
func createResignedIPA(config *ResignConfig, includeSwiftSupport bool) error {
	outputPath := config.OutputPath
	if outputPath == "" {
		timeStr := time.Now().Format("20060102_150405")
		outputPath = filepath.Join(filepath.Dir(config.IPAPath), fmt.Sprintf("%s_resigned_%s.ipa", config.AppName, timeStr))
	}

	logInfo("创建重签名IPA: %s", outputPath)

	// 创建临时目录用于存放最终的IPA文件
	tempIPADir := filepath.Join(config.TempDir, "finalIPA")
	if err := os.MkdirAll(tempIPADir, 0755); err != nil {
		return fmt.Errorf("创建临时IPA目录失败: %w", err)
	}

	// 使用zip命令创建IPA文件，这样可以保留文件权限
	// 首先切换到原IPA解压目录
	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前目录失败: %w", err)
	}

	// 切换到原IPA解压目录
	if err := os.Chdir(config.OriginAppUnzipPath); err != nil {
		return fmt.Errorf("切换到原IPA解压目录失败: %w", err)
	}

	// 使用zip命令创建IPA文件，压缩所有文件和文件夹
	tempIPAPath := filepath.Join(tempIPADir, "temp.ipa")
	logInfo("打包IPA文件，包含原IPA中的所有文件和文件夹")

	// 获取当前目录下的所有文件和文件夹
	entries, err := os.ReadDir(".")
	if err != nil {
		os.Chdir(currentDir)
		return fmt.Errorf("读取原IPA解压目录失败: %w", err)
	}

	// 构建zip命令参数
	args := []string{"-r", tempIPAPath}
	for _, entry := range entries {
		args = append(args, entry.Name())
	}

	cmd := exec.Command("zip", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 切回原目录
		os.Chdir(currentDir)
		return fmt.Errorf("创建IPA文件失败: %s, %w", string(output), err)
	}

	// 切回原目录
	if err := os.Chdir(currentDir); err != nil {
		return fmt.Errorf("切回原目录失败: %w", err)
	}

	// 复制临时IPA文件到最终位置
	if err := copyFile(tempIPAPath, outputPath); err != nil {
		return fmt.Errorf("复制IPA文件失败: %w", err)
	}

	// 如果zip命令创建IPA成功，直接返回
	logSuccess("重签名IPA创建完成: %s", outputPath)
	return nil
}

// 使用标准zip库创建IPA
func createZipIPA(config *ResignConfig, outputPath string) error {
	// 创建新的zip文件
	zipfile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %w", err)
	}
	defer zipfile.Close()

	zipWriter := zip.NewWriter(zipfile)
	defer zipWriter.Close()

	// 计算总文件数
	var totalFiles int
	err = filepath.Walk(config.TempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && info.Name() != "temp.ipa" &&
			(strings.HasPrefix(filepath.ToSlash(path), filepath.ToSlash(filepath.Join(config.TempDir, "Payload"))) ||
				strings.HasPrefix(filepath.ToSlash(path), filepath.ToSlash(filepath.Join(config.TempDir, "SwiftSupport")))) {
			totalFiles++
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("统计文件数量失败: %w", err)
	}

	// 压缩文件
	currentFile := 0
	err = filepath.Walk(config.TempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只包含Payload和SwiftSupport目录
		relPath, err := filepath.Rel(config.TempDir, path)
		if err != nil {
			return err
		}

		if !strings.HasPrefix(filepath.ToSlash(relPath), "Payload") &&
			!strings.HasPrefix(filepath.ToSlash(relPath), "SwiftSupport") {
			return nil
		}

		// 跳过temp.ipa文件
		if info.Name() == "temp.ipa" {
			return nil
		}

		// 处理目录
		if info.IsDir() {
			// 添加目录条目
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}
			header.Name = filepath.ToSlash(relPath) + "/"
			header.Method = zip.Deflate

			_, err = zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}
			return nil
		}

		currentFile++
		percentage := float64(currentFile) * 100 / float64(totalFiles)
		fmt.Printf("\r正在压缩文件: %d/%d (%.0f%%)", currentFile, totalFiles, percentage)

		// 创建zip条目
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}
		header.Name = filepath.ToSlash(relPath)
		header.Method = zip.Deflate

		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}

		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(writer, file)
		return err
	})

	fmt.Println() // 换行
	if err != nil {
		return fmt.Errorf("压缩文件失败: %w", err)
	}

	return nil
}

// ResignIPAExe 执行IPA重签名
func ResignIPAExe(config *ResignConfig) error {
	// 确保是绝对路径
	if !filepath.IsAbs(config.IPAPath) {
		absPath, err := filepath.Abs(config.IPAPath)
		if err != nil {
			return fmt.Errorf("无法获取IPA文件的绝对路径: %w", err)
		}
		config.IPAPath = absPath
	}

	// 设置临时目录
	config.TempDir = filepath.Join(filepath.Dir(config.IPAPath), "resignTemp")
	// 设置原IPA解压目录
	config.OriginAppUnzipPath = filepath.Join(config.TempDir, "unzipOriginApp")
	defer cleanupResign(config)

	// 清理并创建临时目录
	logInfo("清理并创建临时目录: %s", config.TempDir)
	if err := os.RemoveAll(config.TempDir); err != nil {
		return fmt.Errorf("无法清理临时目录: %w", err)
	}
	if err := os.MkdirAll(config.TempDir, 0755); err != nil {
		return fmt.Errorf("无法创建临时目录: %w", err)
	}

	// 创建原IPA解压目录
	logInfo("创建原IPA解压目录: %s", config.OriginAppUnzipPath)
	if err := os.MkdirAll(config.OriginAppUnzipPath, 0755); err != nil {
		return fmt.Errorf("无法创建原IPA解压目录: %w", err)
	}

	// 复制并解压IPA文件
	logInfo("复制并解压IPA文件...")
	if err := copyFile(config.IPAPath, filepath.Join(config.TempDir, "temp.ipa")); err != nil {
		return fmt.Errorf("无法复制IPA文件: %w", err)
	}

	if err := UnzipIPAExe(filepath.Join(config.TempDir, "temp.ipa"), config.OriginAppUnzipPath); err != nil {
		return fmt.Errorf("解压IPA文件失败: %w", err)
	}

	// 获取应用名称
	appName, err := getAppName(config.OriginAppUnzipPath)
	if err != nil {
		return fmt.Errorf("获取应用名称失败: %w", err)
	}
	config.AppName = appName
	logInfo("应用名称: %s", config.AppName)

	// 更新Info.plist
	appPath := filepath.Join(config.OriginAppUnzipPath, "Payload", config.AppName+".app")
	plistPath := filepath.Join(appPath, "Info.plist")
	if err := updateInfoPlist(plistPath, config); err != nil {
		return fmt.Errorf("更新Info.plist失败: %w", err)
	}

	// 处理Info.plist文件的修改和合并
	if err := processPlistModifications(plistPath, config.AdditionalPlistPath, config.RemovePlistKeys); err != nil {
		return fmt.Errorf("处理Info.plist修改失败: %w", err)
	}

	// 安装mobileprovision文件
	if err := installProvisioningProfile(appPath, config.ProvisioningProfile); err != nil {
		return fmt.Errorf("安装mobileprovision失败: %w", err)
	}

	// 替换应用图标
	if err := replaceAppIcon(appPath, config.AppIconPath, config.TempDir); err != nil {
		return fmt.Errorf("替换应用图标失败: %w", err)
	}

	// 替换启动图片
	if err := replaceLaunchImage(appPath, config.LaunchImagePath); err != nil {
		return fmt.Errorf("替换启动图片失败: %w", err)
	}

	// 重新签名
	if err := signApp(config); err != nil {
		return fmt.Errorf("重新签名失败: %w", err)
	}

	// 处理SwiftSupport
	needSwiftSupport := needHandleSwiftSupport(config)
	if needSwiftSupport {
		if err := handleSwiftSupport(config); err != nil {
			return fmt.Errorf("处理SwiftSupport失败: %w", err)
		}
	}

	// 创建重签名后的IPA
	if err := createResignedIPA(config, needSwiftSupport); err != nil {
		return fmt.Errorf("创建重签名IPA失败: %w", err)
	}

	logSuccess("IPA重签名完成!")
	return nil
}
